import numpy as np
import netCDF4 as nc

# NetCDF文件路径
nc_path = "/home/<USER>/OLRpro/olr_data_all.nc"

# 打开NetCDF文件
with nc.Dataset(nc_path, "r") as ds:
    # 读取数据
    olrn_data = ds.variables["olrn"][:]
    olrd_data = ds.variables["olrd"][:]

    # 计算NaN值的数量
    nan_count_olrn = np.isnan(olrn_data).sum()
    nan_count_olrd = np.isnan(olrd_data).sum()

    # 打印结果
    print(f"olrn 中的 NaN 值数量: {nan_count_olrn}")
    print(f"olrd 中的 NaN 值数量: {nan_count_olrd}")
