                    Documentation for Daily OLR

Two data sets will be maintained each day, one containing the OLR data for the
"afternoon" satellite  (i.e. equator crossing time ~ 0230/1430 LST, currently
"NOAA-14") and one data set for the "morniong" satellite (i.e. equator crossing
time ~ 0630/1830 currently "NOAA-15").  These data are stored in files with
names "noaa14_olr" and "noaa15_olr", etc. on the NCEP ftp server in directory
"/pub/precip/data-req".  Each data set will contain two "logical" records: the
1st record in each file contains OLR for the "night" pass (~ 0230 LST & ~0630
LST for the "afternoon" and "morning" satellites, respectively);  the 2nd
record in each file contains OLR for the "day" pass (~1430 LST & ~1830 LST for
the "afternoon" & "morning" satellites, respectively).  These times are
approximate equatorial crossing times which change with time, especially for
the "afternoon" satellites.
--------------------------------------------------------------------------                                                


The data are on a 2.5 deg. lat/lon grid (units are watts/(m*m)).
The data are stored in 144 X 72 arrays in ASCII), and each value 
represents the  OLR flux on a 2.5 X 2.5 deg grid. 
The array orientation is North -> South and eastward from Greenwich.

The 1st row of 144 values contains date information and the polar
values.   The 1st row contains the following information:

     Word 3: year
          4: month
          5: day
          6: data type: "1" means "day" pass
                        "2" means "night" pass
         25: Noth Pole value
         26: South Pole value

 The remaining information is either "-9999" or for internal use




Rows 2 - 72 contain the data for latitudes 87.5N to 87.5S
 
 

             ****************************************                               
--->>>>>>>  1.  Missing values are identified as "-9999" <<<<<<<<------

            2.  Negative values (that are NOT equal to "-9999"
                  are spatially interpolated values)

            3.  Values are in integer with the 1/10's unit
                  preserved, so divide values by "10" and
                  store in a floating point word
             **************************************** 


   A sample FORTRAN program to show the file structure follows:

      integer*4 inite(144,72), iday(144,72)
      real*4    fday(144,71), fnite(144,71)
      open (1,file='............')
c
c------ read "night" pass OLR data
      read(1,105) ((inite(I,J),I=1,144),J=1,72)
 105  format(144I6)
c
c------ read "day" pass OLR data
      read(1,105) ((iday(I,J),I=1,144),J=1,72)
c
      do 300 j=2,72
         do 200 i=1,144
         if(iday(i,j).eq.-9999) then
           fday(i,j)=-9999.
           go to 200
         endif
c
         if(iday(i,j).lt.0) then
c           {decide if you want to accept interpolated values}
           fday(i,j)=(-1.) * float(iday(i,j)/10.
         else
           fday(i,j)=float(iday(i,j)/10.
         endif

       .

       .
       .
      stop 1
      end



-----------------------------------------------------------

To get these data via anonymous ftp, do the following:

1.  ftp ftp.ncep.noaa.gov
2.  userid is <anonymous>
3.  password is your complete e-mail address
4.  when logged in, type cd pub/precip/noaa**_olr  ('**' is satellite ID)
5.  get <filename>, where filename is: #yymmdd,

    where "yymmdd" is year,month day (eg. 930819 = August 19, 1993)


-------------------------------------------------------------


Contact John Janowiak regarding problems or further info. at: 


NOAA/NWS/NMC/Climate Prediction Center
Analysiss Branch
5200 Auth Rd  Room 605
Camp Springs, MD  20746  USA
e-mail: <EMAIL>
Phone:  (US)  ************  ext. 7537
Fax:    (US)  ************

