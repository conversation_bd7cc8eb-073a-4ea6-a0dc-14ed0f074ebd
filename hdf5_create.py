import numpy as np
import h5py
import os

# 数据文件路径
DataPath = "/home/<USER>/OLRpro/ftpdata2019-2021"

# 经纬度设置
onedegreelon = np.arange(0, 360, 1)  # 经度从0到359
onedegreelat = np.arange(90, -91, -1)  # 纬度从90到-90

# 创建二维经纬度网格
Longitude = np.tile(onedegreelon, (180, 1))
Latitude = np.tile(onedegreelat[:, np.newaxis], (1, 360))

# 创建HDF5文件
with h5py.File("olr_data_4.h5", "w") as h5:
    # 创建维度数据集
    lats = h5.create_dataset("lat", data=Latitude)
    lons = h5.create_dataset("lon", data=Longitude)

    # 准备遍历目录中的文件
    file_list = sorted(f for f in os.listdir(DataPath) if "noaa18" in f and f.startswith("noaa18."))[:5]  # 仅读取前5个文件进行测试
    # 创建数据集
    day_olr = h5.create_dataset("day_olr", (len(file_list), 180, 360), dtype=np.float32, compression="gzip")
    night_olr = h5.create_dataset("night_olr", (len(file_list), 180, 360), dtype=np.float32, compression="gzip")
    dates = h5.create_dataset("date", (len(file_list),), dtype=np.int32)  # 日期数据集

    # 读取数据并写入HDF5
    for i, filename in enumerate(file_list):
        filepath = os.path.join(DataPath, filename)
        with open(filepath, "rb") as file:
            # 读取数据并转置
            data = np.fromfile(file, dtype=np.float32).reshape((360, 360)).T  # 使用float32读取

        # 解析日期
        date_str = filename.split(".")[1]  # 文件名格式为"noaa18.20190101"
        year = int(date_str[:4])
        month = int(date_str[4:6])
        day = int(date_str[6:8])
        date_int = year * 10000 + month * 100 + day  # 创建整数日期 YYYYMMDD

        # 分割数据为白天和夜晚
        DayData = data[:180, :]
        NightData = data[180:360, :]

        # 写入HDF5
        day_olr[i, :, :] = DayData
        night_olr[i, :, :] = NightData
        dates[i] = date_int  # 存储日期
