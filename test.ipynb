{"cells": [{"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据已成功保存到 /home/<USER>/OLRpro/olr_data_6.h5\n"]}], "source": ["import numpy as np\n", "import h5py\n", "import os\n", "\n", "# 数据文件夹路径\n", "data_folder = \"/home/<USER>/OLRpro/ftpdata2019-2021\"\n", "files = sorted([f for f in os.listdir(data_folder) if f.startswith(\"noaa18.\")])[:5]\n", "\n", "# HDF5文件保存路径\n", "hdf5_path = \"/home/<USER>/OLRpro/olr_data.h5\"\n", "\n", "# 经纬度数组\n", "longitude = np.arange(0.5, 360.5, 1)  # 360个点，从0.5到359.5\n", "latitude = np.arange(89.5, -90.5, -1)  # 180个点，从89.5到-89.5\n", "\n", "# 数据类型和大小设定\n", "dtype = np.dtype(\">f4\")\n", "expected_size = 360 * 180\n", "\n", "# 创建HDF5文件并准备数据集\n", "with h5py.File(hdf5_path, \"w\") as hdf:\n", "    # 创建经度和纬度数据集\n", "    hdf.create_dataset(\"longitude\", data=longitude)\n", "    hdf.create_dataset(\"latitude\", data=latitude)\n", "\n", "    # 创建夜间和白天数据的数据集，分别存储所有文件的数据\n", "    olrn_all = hdf.create_dataset(\"olrn\", (len(files), 180, 360), dtype=\"float32\", compression=\"gzip\")\n", "    olrd_all = hdf.create_dataset(\"olrd\", (len(files), 180, 360), dtype=\"float32\", compression=\"gzip\")\n", "\n", "    for index, filename in enumerate(files):\n", "        file_path = os.path.join(data_folder, filename)\n", "\n", "        with open(file_path, \"rb\") as file:\n", "            data = np.fromfile(file, dtype=dtype)\n", "\n", "        if data.size == 2 * expected_size:\n", "            # 分割数据为夜间（olrn）和白天（olrd）\n", "            olrn_data = data[:expected_size].reshape((180, 360))\n", "            olrd_data = data[expected_size:].reshape((180, 360))\n", "\n", "            # 转换未定义值\n", "            olrn_data[olrn_data == -9999.0] = np.nan\n", "            olrd_data[olrd_data == -9999.0] = np.nan\n", "\n", "            # 存储到对应的数据集中\n", "            olrn_all[index, :, :] = olrn_data\n", "            olrd_all[index, :, :] = olrd_data\n", "        else:\n", "            print(f\"文件 {filename} 的数据大小不匹配: {data.size}, 期望 {2 * expected_size}\")\n", "\n", "print(f\"数据已成功保存到 {hdf5_path}\")"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文件权限: 0o100664\n"]}], "source": ["import os\n", "\n", "file_path = \"/home/<USER>/OLRpro/olr_data_6.h5\"\n", "\n", "# 检查文件是否存在\n", "if os.path.exists(file_path):\n", "    # 获取文件的访问权限\n", "    permissions = os.stat(file_path)\n", "    print(\"文件权限:\", oct(permissions.st_mode))  # 显示八进制权限表示\n", "else:\n", "    print(\"文件不存在，请检查文件路径。\")"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['latitude', 'longitude', 'olrd', 'olrn']\n"]}], "source": ["import h5py\n", "\n", "file_path = \"/home/<USER>/OLRpro/olr_data_6.h5\"\n", "try:\n", "    with h5py.File(file_path, \"r\") as f:\n", "        print(list(f.keys()))  # 尝试列出文件中的数据集\n", "except Exception as e:\n", "    print(f\"无法打开文件: {e}\")"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文件中的数据集:\n", "latitude (180,)\n", "longitude (360,)\n", "olrd (5, 180, 360)\n", "olrn (5, 180, 360)\n", "olrn 数据集的部分数据:\n", "[[165. 165. 165. 165. 165.]\n", " [167. 167. 167. 167. 167.]\n", " [170. 170. 170. 170. 170.]\n", " [170. 170. 170. 170. 170.]\n", " [168. 168. 168. 168. 168.]]\n"]}], "source": ["import h5py\n", "\n", "# 打开HDF5文件\n", "hdf5_path = \"/home/<USER>/OLRpro/olr_data_6.h5\"\n", "with h5py.File(file_path, \"r\") as f:\n", "    print(\"文件中的数据集:\")\n", "    for key in f.keys():\n", "        print(key, f[key].shape)  # 打印每个数据集的名称和形状\n", "\n", "    # 可选：打印具体某个数据集的一部分数据\n", "    data = f[\"olrn\"][:]  # 假设我们检查 'olrn' 数据集\n", "    print(\"olrn 数据集的部分数据:\")\n", "    print(data[0, :5, :5])  # 打印第一个时间点的前5x5数据"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据已成功保存到 /home/<USER>/OLRpro/olr_data.nc\n"]}], "source": ["import numpy as np\n", "import netCDF4 as nc\n", "import os\n", "\n", "# 数据文件夹路径\n", "data_folder = \"/home/<USER>/OLRpro/ftpdata2019-2021\"\n", "files = sorted([f for f in os.listdir(data_folder) if f.startswith(\"noaa18.\")])[:5]\n", "\n", "# NetCDF文件保存路径\n", "nc_path = \"/home/<USER>/OLRpro/olr_data.nc\"\n", "\n", "# 经纬度数组\n", "longitude = np.arange(0.5, 360.5, 1)  # 360个点，从0.5到359.5\n", "latitude = np.arange(89.5, -90.5, -1)  # 180个点，从89.5到-89.5\n", "\n", "# 数据类型和大小设定\n", "dtype = np.dtype(\">f4\")\n", "expected_size = 360 * 180\n", "\n", "# 创建NetCDF文件并准备数据集\n", "with nc.Dataset(nc_path, \"w\", format=\"NETCDF4\") as ds:\n", "    # 创建维度\n", "    ds.createDimension(\"time\", None)  # 时间维度，无限制\n", "    ds.createDimension(\"lat\", len(latitude))\n", "    ds.createDimension(\"lon\", len(longitude))\n", "\n", "    # 创建变量\n", "    times = ds.createVariable(\"time\", \"i4\", (\"time\",))\n", "    lats = ds.createVariable(\"lat\", \"f4\", (\"lat\",))\n", "    lons = ds.createVariable(\"lon\", \"f4\", (\"lon\",))\n", "    olrn_all = ds.createVariable(\"olrn\", \"f4\", (\"time\", \"lat\", \"lon\"), fill_value=np.nan)\n", "    olrd_all = ds.createVariable(\"olrd\", \"f4\", (\"time\", \"lat\", \"lon\"), fill_value=np.nan)\n", "\n", "    # 设置经纬度数据\n", "    lats[:] = latitude\n", "    lons[:] = longitude\n", "\n", "    # 读取并存储每个文件的数据\n", "    for index, filename in enumerate(files):\n", "        file_path = os.path.join(data_folder, filename)\n", "\n", "        with open(file_path, \"rb\") as file:\n", "            data = np.fromfile(file, dtype=dtype)\n", "\n", "        if data.size == 2 * expected_size:\n", "            # 分割数据为夜间（olrn）和白天（olrd）\n", "            olrn_data = data[:expected_size].reshape((180, 360))\n", "            olrd_data = data[expected_size:].reshape((180, 360))\n", "\n", "            # 处理未定义值\n", "            olrn_data[olrn_data == -9999.0] = np.nan\n", "            olrd_data[olrd_data == -9999.0] = np.nan\n", "\n", "            # 存储数据\n", "            olrn_all[index, :, :] = olrn_data\n", "            olrd_all[index, :, :] = olrd_data\n", "            times[index] = index  # 假设时间索引简单递增\n", "        else:\n", "            print(f\"文件 {filename} 的数据大小不匹配: {data.size}, 期望 {2 * expected_size}\")\n", "\n", "print(f\"数据已成功保存到 {nc_path}\")"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据已成功保存到 /home/<USER>/OLRpro/olr_data_20.nc\n"]}], "source": ["import numpy as np\n", "import netCDF4 as nc\n", "import os\n", "\n", "# 数据文件夹路径\n", "data_folder = \"/home/<USER>/OLRpro/ftpdata2019-2021\"\n", "files = sorted([f for f in os.listdir(data_folder) if f.startswith(\"noaa18.\")])[:5]\n", "\n", "# NetCDF文件保存路径\n", "nc_path = \"/home/<USER>/OLRpro/olr_data_20.nc\"\n", "\n", "# 经纬度设置\n", "onedegreelon = np.arange(0.5, 360.5, 1)  # 从0.5到359.5\n", "onedegreelat = np.arange(89.5, -90.5, -1)  # 从89.5到-89.5\n", "\n", "# 数据类型和大小设定\n", "dtype = np.dtype(\">f4\")\n", "expected_size = 360 * 180\n", "\n", "# 创建NetCDF文件并准备数据集\n", "with nc.Dataset(nc_path, \"w\", format=\"NETCDF4\") as ds:\n", "    # 创建维度\n", "    ds.createDimension(\"index\", len(files))  # 用于标记时间的索引\n", "    ds.createDimension(\"lat\", 180)\n", "    ds.createDimension(\"lon\", 360)\n", "\n", "    # 创建变量\n", "    times = ds.createVariable(\"time\", \"i4\", (\"index\",))  # 存储具体日期\n", "    lats = ds.createVariable(\"lat\", \"f4\", (\"lat\",))\n", "    lons = ds.createVariable(\"lon\", \"f4\", (\"lon\",))\n", "    olrn_all = ds.createVariable(\"olrn\", \"f4\", (\"index\", \"lat\", \"lon\"), fill_value=np.nan)\n", "    olrd_all = ds.createVariable(\"olrd\", \"f4\", (\"index\", \"lat\", \"lon\"), fill_value=np.nan)\n", "\n", "    # 设置经纬度数据\n", "    lats[:] = onedegreelat\n", "    lons[:] = onedegreelon\n", "\n", "    # 读取并存储每个文件的数据\n", "    dates = []\n", "    for index, filename in enumerate(files):\n", "        file_path = os.path.join(data_folder, filename)\n", "\n", "        with open(file_path, \"rb\") as file:\n", "            data = np.fromfile(file, dtype=dtype)\n", "\n", "        if data.size == 2 * expected_size:\n", "            olrn_data = data[:expected_size].reshape((180, 360))\n", "            olrd_data = data[expected_size:].reshape((180, 360))\n", "            olrn_data[olrn_data == -9999.0] = np.nan\n", "            olrd_data[olrd_data == -9999.0] = np.nan\n", "\n", "            # 解析日期\n", "            date_str = filename.split(\".\")[1]  # 文件名格式为\"noaa18.20190101\"\n", "            year, month, day = int(date_str[:4]), int(date_str[4:6]), int(date_str[6:8])\n", "            date_int = year * 10000 + month * 100 + day\n", "            dates.append(date_int)\n", "\n", "            # 存储数据\n", "            times[index] = date_int\n", "            olrn_all[index, :, :] = olrn_data\n", "            olrd_all[index, :, :] = olrd_data\n", "        else:\n", "            print(f\"文件 {filename} 的数据大小不匹配: {data.size}, 期望 {2 * expected_size}\")\n", "\n", "print(f\"数据已成功保存到 {nc_path}\")"]}], "metadata": {"kernelspec": {"display_name": "gpytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}