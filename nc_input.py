import numpy as np
import netCDF4 as nc
import os

# 数据文件夹路径
data_folder = "/home/<USER>/OLRpro/ftpdata2019-2021"
files = sorted([f for f in os.listdir(data_folder) if f.startswith("noaa18.")])

# NetCDF文件保存路径
nc_path = "/home/<USER>/OLRpro/olr_data_all.nc"

# 经纬度设置
onedegreelon = np.arange(0.5, 360.5, 1)  # 从0.5到359.5
onedegreelat = np.arange(89.5, -90.5, -1)  # 从89.5到-89.5

# 数据类型和大小设定
dtype = np.dtype(">f4")
expected_size = 360 * 180

# 创建NetCDF文件并准备数据集
with nc.Dataset(nc_path, "w", format="NETCDF4") as ds:
    # 创建维度
    ds.createDimension("index", len(files))  # 用于标记时间的索引
    ds.createDimension("lat", 180)
    ds.createDimension("lon", 360)

    # 创建变量
    times = ds.createVariable("time", "i4", ("index",))  # 存储具体日期
    lats = ds.createVariable("lat", "f4", ("lat",))
    lons = ds.createVariable("lon", "f4", ("lon",))
    olrn_all = ds.createVariable("olrn", "f4", ("index", "lat", "lon"), fill_value=np.nan)
    olrd_all = ds.createVariable("olrd", "f4", ("index", "lat", "lon"), fill_value=np.nan)

    # 设置经纬度数据
    lats[:] = onedegreelat
    lons[:] = onedegreelon

    # 读取并存储每个文件的数据
    dates = []
    for index, filename in enumerate(files):
        file_path = os.path.join(data_folder, filename)

        with open(file_path, "rb") as file:
            data = np.fromfile(file, dtype=dtype)

        if data.size == 2 * expected_size:
            olrn_data = data[:expected_size].reshape((180, 360))
            olrd_data = data[expected_size:].reshape((180, 360))
            olrn_data[olrn_data == -9999.0] = np.nan
            olrd_data[olrd_data == -9999.0] = np.nan

            # 解析日期
            date_str = filename.split(".")[1]  # 文件名格式为"noaa18.20190101"
            year, month, day = int(date_str[:4]), int(date_str[4:6]), int(date_str[6:8])
            date_int = year * 10000 + month * 100 + day
            dates.append(date_int)

            # 存储数据
            times[index] = date_int
            olrn_all[index, :, :] = olrn_data
            olrd_all[index, :, :] = olrd_data
        else:
            print(f"文件 {filename} 的数据大小不匹配: {data.size}, 期望 {2 * expected_size}")

print(f"数据已成功保存到 {nc_path}")
