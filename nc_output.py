import numpy as np
import netCDF4 as nc

# NetCDF文件路径
nc_path = "/home/<USER>/OLRpro/olr_data_all.nc"
# 输出的文本文件路径
txt_path = "/home/<USER>/OLRpro/olr_data.txt"

# 打开NetCDF文件
with nc.Dataset(nc_path, "r") as ds:
    # 读取数据
    times = ds.variables["time"][:]
    lons = ds.variables["lon"][:]
    lats = ds.variables["lat"][:]
    olrn = ds.variables["olrn"][:]
    olrd = ds.variables["olrd"][:]
    diff_olrn = ds.variables["diff_olrn"][:]
    diff_olrd = ds.variables["diff_olrd"][:]

    # 打开文本文件准备写入
    with open(txt_path, "w") as f:
        # 写入标题头
        f.write("Date,Longitude,Latitude,Daytime,Daytime_diff,Nighttime,Nighttime_diff\n")

        # 遍历每个时间点和地理位置
        for time_index, date in enumerate(times):
            for lat_idx in range(len(lats)):
                for lon_idx in range(len(lons)):
                    # 读取对应数据
                    day_value = olrd[time_index, lat_idx, lon_idx]
                    night_value = olrn[time_index, lat_idx, lon_idx]
                    day_diff = diff_olrd[time_index, lat_idx, lon_idx]
                    night_diff = diff_olrn[time_index, lat_idx, lon_idx]
                    # 写入一行数据
                    f.write(f"{date},{lons[lon_idx]:.2f},{lats[lat_idx]:.2f},{day_value:.2f},{day_diff:.2f},{night_value:.2f},{night_diff:.2f}\n")

print("数据已成功导出到文本文件。")
