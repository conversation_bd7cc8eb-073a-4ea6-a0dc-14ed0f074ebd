import numpy as np
import netCDF4 as nc

# NetCDF文件路径
nc_path = "/home/<USER>/OLRpro/olr_data_all.nc"

# 打开NetCDF文件
with nc.Dataset(nc_path, "a") as ds:  # 使用 'a' 模式打开文件以便添加数据
    # 读取olrn和olrd变量的数据
    olrn_data = ds.variables["olrn"][:]
    olrd_data = ds.variables["olrd"][:]

    # 计算平均值，忽略NaN值
    mean_olrn = np.nanmean(olrn_data, axis=0)  # 对时间维度进行平均
    mean_olrd = np.nanmean(olrd_data, axis=0)  # 对时间维度进行平均

    # 创建新变量来存储平均值
    if "mean_olrn" not in ds.variables:  # 检查变量是否已存在
        mean_olrn_var = ds.createVariable("mean_olrn", "f4", ("lat", "lon"))
        mean_olrn_var[:] = mean_olrn  # 存储计算得到的平均值
    if "mean_olrd" not in ds.variables:
        mean_olrd_var = ds.createVariable("mean_olrd", "f4", ("lat", "lon"))
        mean_olrd_var[:] = mean_olrd

    print("平均值已经被计算并存储在文件中。")

# 打印操作完成信息
print("操作完成，平均值已被成功添加到文件。")
