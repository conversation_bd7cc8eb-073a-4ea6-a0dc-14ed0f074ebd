import numpy as np
import netCDF4 as nc

# NetCDF文件路径
nc_path = "/home/<USER>/OLRpro/olr_data_all.nc"

# 打开NetCDF文件以附加模式
with nc.Dataset(nc_path, "a") as ds:
    # 读取olrn和olrd变量的数据
    olrn_data = ds.variables["olrn"][:]
    olrd_data = ds.variables["olrd"][:]

    # 读取平均值数据
    mean_olrn = ds.variables["mean_olrn"][:]
    mean_olrd = ds.variables["mean_olrd"][:]

    # 计算差值
    diff_olrn = olrn_data - mean_olrn
    diff_olrd = olrd_data - mean_olrd

    # 创建新变量来存储差值
    if "diff_olrn" not in ds.variables:  # 检查变量是否已存在
        diff_olrn_var = ds.createVariable("diff_olrn", "f4", ("index", "lat", "lon"), fill_value=np.nan)
        diff_olrn_var[:] = diff_olrn  # 存储计算得到的差值
    if "diff_olrd" not in ds.variables:
        diff_olrd_var = ds.createVariable("diff_olrd", "f4", ("index", "lat", "lon"), fill_value=np.nan)
        diff_olrd_var[:] = diff_olrd

    print("差值已经被计算并存储在文件中。")

# 打印操作完成信息
print("操作完成，差值已被成功添加到文件。")
